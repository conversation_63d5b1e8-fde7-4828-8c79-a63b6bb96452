console.log('🚀 Starting Simple Performance Test...')

// Test data generation
function generateTestData(count) {
  console.log(`📊 Generating ${count} test records...`)
  const start = Date.now()
  
  const data = []
  for (let i = 1; i <= count; i++) {
    data.push({
      id: i,
      name: `Test User ${i}`,
      email: `user${i}@test.com`,
      phone: `05${String(Math.floor(Math.random() * 90000000) + 10000000)}`,
      address: `Address ${i}`,
      notes: `Test notes for record ${i}`
    })
  }
  
  const end = Date.now()
  console.log(`✅ Generated ${count} records in ${end - start}ms`)
  return data
}

// Test search performance
function testSearch(data, searchTerm) {
  console.log(`🔍 Testing search for "${searchTerm}"...`)
  const start = Date.now()
  
  const results = data.filter(record => 
    Object.values(record).some(value => 
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  )
  
  const end = Date.now()
  console.log(`✅ Found ${results.length} matches in ${end - start}ms`)
  return { matches: results.length, time: end - start }
}

// Test CSV export
function testCSVExport(data) {
  console.log('📄 Testing CSV export...')
  const start = Date.now()
  
  const headers = Object.keys(data[0]).join(',')
  const rows = data.map(row => Object.values(row).join(','))
  const csv = [headers, ...rows].join('\n')
  
  const end = Date.now()
  console.log(`✅ Exported ${data.length} records to CSV in ${end - start}ms`)
  console.log(`📁 CSV size: ${(csv.length / 1024).toFixed(2)} KB`)
  return { time: end - start, size: csv.length }
}

// Run tests
async function runTests() {
  console.log('=' * 50)
  console.log('🧪 PERFORMANCE TEST SUITE')
  console.log('=' * 50)
  
  const testSizes = [1000, 5000, 10000]
  const searchTerms = ['Test', 'User', '123', 'test.com']
  
  for (const size of testSizes) {
    console.log(`\n📊 Testing with ${size} records:`)
    console.log('-' * 30)
    
    // Generate data
    const data = generateTestData(size)
    
    // Test searches
    for (const term of searchTerms) {
      testSearch(data, term)
    }
    
    // Test export
    testCSVExport(data)
    
    // Memory usage
    const memory = process.memoryUsage()
    console.log(`💾 Memory: ${(memory.heapUsed / 1024 / 1024).toFixed(2)} MB`)
  }
  
  console.log('\n✅ All tests completed!')
  console.log('\n💡 Recommendations for Electron version:')
  console.log('   - Use SQLite with indexes for better search performance')
  console.log('   - Implement pagination for large datasets')
  console.log('   - Use streaming for file operations')
  console.log('   - Add full-text search capabilities')
}

// Run the tests
runTests().catch(console.error)

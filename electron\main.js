const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'
const dataService = require('./data-service')
const Store = require('electron-store')
const appUpdater = require('./updater')

// Keep a global reference of the window object
let mainWindow

// Initialize electron store for settings
const store = new Store()

function createWindow() {
  // Create the browser window with enhanced security
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      sandbox: false, // Disabled for database access
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      enableBlinkFeatures: '',
      disableBlinkFeatures: 'Auxclick'
    },
    icon: path.join(__dirname, '../public/icons/icon.png'),
    show: false,
    titleBarStyle: 'default',
    frame: true,
    transparent: false,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    alwaysOnTop: false,
    fullscreenable: true,
    skipTaskbar: false
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5175')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // Setup updater with main window reference
    appUpdater.setMainWindow(mainWindow)

    // Check for updates after window is shown (delay to avoid blocking startup)
    setTimeout(() => {
      appUpdater.checkForUpdates().catch(console.error)
      // Schedule periodic update checks
      appUpdater.scheduleUpdateChecks()
    }, 5000)
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Set up menu
  createMenu()
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'البحث عن تحديثات',
          click: () => {
            appUpdater.manualUpdateCheck().catch(console.error)
          }
        },
        { type: 'separator' },
        {
          label: 'حول البرنامج',
          click: () => {
            // Show about dialog
            const options = {
              type: 'info',
              title: 'حول البرنامج',
              message: 'برنامج بيانات المتهمين',
              detail: `الإصدار: ${app.getVersion()}\nنظام متكامل لإدارة بيانات المتهمين\n\nتم التطوير باستخدام:\n- Electron\n- Vue.js 3\n- TypeScript\n- SQLite`,
              buttons: ['موافق'],
              defaultId: 0
            }
            dialog.showMessageBox(mainWindow, options)
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// Initialize data service and IPC handlers
async function initializeApp() {
  try {
    // Initialize database and data service
    await dataService.initialize()
    console.log('✅ Data service initialized')

    // Setup IPC handlers
    setupIpcHandlers()

    // Create main window
    createWindow()
  } catch (error) {
    console.error('❌ Failed to initialize app:', error)
    app.quit()
  }
}

// Setup IPC handlers for communication with renderer
function setupIpcHandlers() {
  // Activation handlers
  ipcMain.handle('activation:save', async (event, data) => {
    return await dataService.saveActivationStatus(data)
  })

  ipcMain.handle('activation:check', async () => {
    return await dataService.checkActivationStatus()
  })

  ipcMain.handle('activation:clear', async () => {
    return await dataService.clearActivationStatus()
  })

  // Database handlers
  ipcMain.handle('db:query', async (event, sql, params) => {
    // For security, only allow specific queries
    // This is a simplified implementation
    return { error: 'Direct SQL queries not allowed for security' }
  })

  ipcMain.handle('db:suspects:search', async (event, query, options) => {
    return await dataService.searchSuspects(query, options)
  })

  ipcMain.handle('db:suspects:get', async (event, page, limit) => {
    return await dataService.getSuspects(page, limit)
  })

  ipcMain.handle('db:suspects:add', async (event, data, userId) => {
    return await dataService.addSuspect(data, userId)
  })

  ipcMain.handle('db:suspects:update', async (event, id, data, userId) => {
    return await dataService.updateSuspect(id, data, userId)
  })

  ipcMain.handle('db:suspects:delete', async (event, id, userId) => {
    return await dataService.deleteSuspect(id, userId)
  })

  // File operations
  ipcMain.handle('dialog:openFile', async () => {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })
    return result
  })

  ipcMain.handle('dialog:saveFile', async (event, data) => {
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'Excel Files', extensions: ['xlsx'] },
        { name: 'PDF Files', extensions: ['pdf'] }
      ]
    })
    return result
  })

  // Import operations
  ipcMain.handle('import:csv', async (event, filePath, options) => {
    return new Promise((resolve) => {
      dataService.importCSV(filePath, options, (progress) => {
        // Send progress updates to renderer
        event.sender.send('import:progress', progress)
      }).then(resolve).catch(resolve)
    })
  })

  // App info
  ipcMain.handle('app:getVersion', () => {
    return app.getVersion()
  })

  // Update handlers
  ipcMain.handle('updater:checkForUpdates', async () => {
    return await appUpdater.manualUpdateCheck()
  })

  ipcMain.handle('updater:downloadUpdate', async () => {
    return await appUpdater.downloadUpdate()
  })

  ipcMain.handle('updater:quitAndInstall', () => {
    return appUpdater.quitAndInstall()
  })

  ipcMain.handle('updater:getStatus', () => {
    return appUpdater.getUpdateStatus()
  })

  // Window controls
  ipcMain.handle('window:minimize', () => {
    mainWindow.minimize()
  })

  ipcMain.handle('window:maximize', () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize()
    } else {
      mainWindow.maximize()
    }
  })

  ipcMain.handle('window:close', () => {
    mainWindow.close()
  })

  console.log('✅ IPC handlers setup complete')
}

// App event handlers
app.whenReady().then(initializeApp)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Enhanced Security Policies
app.on('web-contents-created', (event, contents) => {
  // Prevent new window creation
  contents.on('new-window', (event, navigationUrl) => {
    console.log('🚫 Blocked new window creation:', navigationUrl)
    event.preventDefault()
  })

  // Prevent navigation to external URLs
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    // Allow localhost and file:// protocols only
    if (parsedUrl.protocol !== 'file:' &&
        parsedUrl.protocol !== 'http:' &&
        parsedUrl.protocol !== 'https:') {
      console.log('🚫 Blocked navigation to:', navigationUrl)
      event.preventDefault()
    }

    // Block external navigation in production
    if (!isDev && !navigationUrl.startsWith('file://')) {
      console.log('🚫 Blocked external navigation:', navigationUrl)
      event.preventDefault()
    }
  })

  // Prevent opening external links
  contents.setWindowOpenHandler(({ url }) => {
    console.log('🚫 Blocked window open:', url)
    return { action: 'deny' }
  })
})

// Security: Prevent protocol handler abuse
app.on('web-contents-created', (event, contents) => {
  contents.on('will-redirect', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    if (parsedUrl.protocol !== 'file:' &&
        parsedUrl.protocol !== 'http:' &&
        parsedUrl.protocol !== 'https:') {
      console.log('🚫 Blocked redirect to:', navigationUrl)
      event.preventDefault()
    }
  })
})

// Security: Certificate error handling
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev) {
    // In development, ignore certificate errors for localhost
    event.preventDefault()
    callback(true)
  } else {
    // In production, use default behavior (reject invalid certificates)
    callback(false)
  }
})

// Security: Disable node integration in child windows
app.on('browser-window-created', (event, window) => {
  window.webContents.on('new-window', (event, url, frameName, disposition, options) => {
    if (options.webPreferences) {
      options.webPreferences.nodeIntegration = false
      options.webPreferences.contextIsolation = true
    }
  })
})

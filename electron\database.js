const Database = require('better-sqlite3')
const path = require('path')
const fs = require('fs')
const { app } = require('electron')

class SuspectsDatabase {
  constructor() {
    this.db = null
    this.isInitialized = false
  }

  // Initialize database
  async initialize() {
    try {
      // Create data directory if it doesn't exist
      const userDataPath = app.getPath('userData')
      const dbDir = path.join(userDataPath, 'data')
      
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // Database file path
      const dbPath = path.join(dbDir, 'suspects.db')
      console.log('📁 Database path:', dbPath)

      // Open database connection
      this.db = new Database(dbPath)
      this.db.pragma('journal_mode = WAL') // Enable WAL mode for better performance
      this.db.pragma('synchronous = NORMAL') // Balance between safety and performance
      this.db.pragma('cache_size = 10000') // Increase cache size for better performance
      this.db.pragma('temp_store = MEMORY') // Store temporary tables in memory

      // Create tables
      await this.createTables()
      
      // Create indexes for optimized search
      await this.createIndexes()
      
      // Enable full-text search
      await this.enableFullTextSearch()

      this.isInitialized = true
      console.log('✅ SQLite database initialized successfully')
      
      return true
    } catch (error) {
      console.error('❌ Failed to initialize database:', error)
      throw error
    }
  }

  // Create database tables
  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        fullName TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        isActive INTEGER DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Suspects table - main data
      `CREATE TABLE IF NOT EXISTS suspects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        data TEXT NOT NULL, -- JSON data
        searchText TEXT, -- Concatenated searchable text
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        createdBy INTEGER,
        updatedBy INTEGER,
        FOREIGN KEY (createdBy) REFERENCES users(id),
        FOREIGN KEY (updatedBy) REFERENCES users(id)
      )`,

      // Fields configuration
      `CREATE TABLE IF NOT EXISTS fields (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        inputType TEXT NOT NULL,
        isRequired INTEGER DEFAULT 0,
        isVisible INTEGER DEFAULT 1,
        fieldOrder INTEGER DEFAULT 0,
        options TEXT, -- JSON for select options
        validation TEXT, -- JSON for validation rules
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Attachments
      `CREATE TABLE IF NOT EXISTS attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suspectId INTEGER,
        fieldId INTEGER,
        fileName TEXT NOT NULL,
        fileType TEXT,
        fileSize INTEGER,
        filePath TEXT,
        uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (suspectId) REFERENCES suspects(id) ON DELETE CASCADE,
        FOREIGN KEY (fieldId) REFERENCES fields(id)
      )`,

      // Settings
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        data TEXT NOT NULL, -- JSON data
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Reports
      `CREATE TABLE IF NOT EXISTS reports (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        template TEXT, -- JSON template
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        createdBy INTEGER,
        FOREIGN KEY (createdBy) REFERENCES users(id)
      )`,

      // Audit log
      `CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId INTEGER,
        action TEXT NOT NULL,
        resource TEXT NOT NULL,
        resourceId INTEGER,
        details TEXT, -- JSON details
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id)
      )`,

      // Backup records
      `CREATE TABLE IF NOT EXISTS backup_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fileName TEXT NOT NULL,
        filePath TEXT,
        fileSize INTEGER,
        type TEXT DEFAULT 'manual',
        status TEXT DEFAULT 'completed',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Activation status
      `CREATE TABLE IF NOT EXISTS activation_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        activated INTEGER DEFAULT 0,
        activationDate DATETIME,
        timestamp INTEGER,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ]

    for (const sql of tables) {
      this.db.exec(sql)
    }

    console.log('✅ Database tables created successfully')
  }

  // Create optimized indexes
  async createIndexes() {
    const indexes = [
      // Users indexes
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_active ON users(isActive)',

      // Suspects indexes for fast search
      'CREATE INDEX IF NOT EXISTS idx_suspects_created ON suspects(createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_updated ON suspects(updatedAt)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_creator ON suspects(createdBy)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_search ON suspects(searchText)',

      // Attachments indexes
      'CREATE INDEX IF NOT EXISTS idx_attachments_suspect ON attachments(suspectId)',
      'CREATE INDEX IF NOT EXISTS idx_attachments_field ON attachments(fieldId)',
      'CREATE INDEX IF NOT EXISTS idx_attachments_type ON attachments(fileType)',

      // Audit log indexes
      'CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(userId)',
      'CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)',
      'CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_log(resource)',
      'CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)',

      // Settings indexes
      'CREATE INDEX IF NOT EXISTS idx_settings_type ON settings(type)',

      // Backup records indexes
      'CREATE INDEX IF NOT EXISTS idx_backup_created ON backup_records(createdAt)',
      'CREATE INDEX IF NOT EXISTS idx_backup_type ON backup_records(type)'
    ]

    for (const sql of indexes) {
      this.db.exec(sql)
    }

    console.log('✅ Database indexes created successfully')
  }

  // Enable full-text search
  async enableFullTextSearch() {
    try {
      // Create FTS virtual table for suspects
      this.db.exec(`
        CREATE VIRTUAL TABLE IF NOT EXISTS suspects_fts USING fts5(
          id UNINDEXED,
          searchText,
          content='suspects',
          content_rowid='id'
        )
      `)

      // Create triggers to keep FTS table in sync
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_insert AFTER INSERT ON suspects BEGIN
          INSERT INTO suspects_fts(id, searchText) VALUES (new.id, new.searchText);
        END
      `)

      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_delete AFTER DELETE ON suspects BEGIN
          DELETE FROM suspects_fts WHERE id = old.id;
        END
      `)

      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS suspects_fts_update AFTER UPDATE ON suspects BEGIN
          DELETE FROM suspects_fts WHERE id = old.id;
          INSERT INTO suspects_fts(id, searchText) VALUES (new.id, new.searchText);
        END
      `)

      console.log('✅ Full-text search enabled successfully')
    } catch (error) {
      console.warn('⚠️ Full-text search setup failed:', error.message)
      // Continue without FTS if it fails
    }
  }

  // Get database instance
  getDatabase() {
    if (!this.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.db
  }

  // Close database connection
  close() {
    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
      console.log('✅ Database connection closed')
    }
  }

  // Health check
  healthCheck() {
    try {
      const result = this.db.prepare('SELECT 1 as test').get()
      return result.test === 1
    } catch (error) {
      console.error('❌ Database health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
const database = new SuspectsDatabase()
module.exports = database

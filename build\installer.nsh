; Custom NSIS installer script for Suspects Data Management
; This script adds Arabic language support and custom installation steps

!include "MUI2.nsh"
!include "FileFunc.nsh"

; Arabic language support
!insertmacro MUI_LANGUAGE "Arabic"
!insertmacro MUI_LANGUAGE "English"

; Custom strings in Arabic
LangString WELCOME_TEXT ${LANG_ARABIC} "مرحباً بك في معالج تثبيت برنامج بيانات المتهمين"
LangString WELCOME_TEXT ${LANG_ENGLISH} "Welcome to the Suspects Data Management Setup Wizard"

LangString FINISH_TEXT ${LANG_ARABIC} "تم تثبيت البرنامج بنجاح. يمكنك الآن تشغيله من سطح المكتب أو قائمة ابدأ."
LangString FINISH_TEXT ${LANG_ENGLISH} "The application has been installed successfully. You can now run it from the desktop or start menu."

LangString REQUIREMENTS_TEXT ${LANG_ARABIC} "متطلبات النظام:$\r$\n- Windows 7 أو أحدث$\r$\n- 4 GB ذاكرة وصول عشوائي$\r$\n- 500 MB مساحة فارغة"
LangString REQUIREMENTS_TEXT ${LANG_ENGLISH} "System Requirements:$\r$\n- Windows 7 or later$\r$\n- 4 GB RAM$\r$\n- 500 MB free space"

; Custom pages
!define MUI_WELCOMEPAGE_TITLE "$(WELCOME_TEXT)"
!define MUI_WELCOMEPAGE_TEXT "$(REQUIREMENTS_TEXT)"

!define MUI_FINISHPAGE_TITLE "$(FINISH_TEXT)"
!define MUI_FINISHPAGE_RUN "$INSTDIR\${PRODUCT_NAME}.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل البرنامج الآن"

; Pre-installation checks
Function .onInit
  ; Check Windows version
  ${IfNot} ${AtLeastWin7}
    MessageBox MB_OK|MB_ICONSTOP "هذا البرنامج يتطلب Windows 7 أو أحدث."
    Abort
  ${EndIf}
  
  ; Check available disk space (500 MB minimum)
  ${GetRoot} "$INSTDIR" $0
  ${DriveSpace} "$0" "/D=F /S=M" $1
  ${If} $1 < 500
    MessageBox MB_OK|MB_ICONSTOP "مساحة القرص غير كافية. يتطلب البرنامج 500 ميجابايت على الأقل."
    Abort
  ${EndIf}
FunctionEnd

; Post-installation tasks
Function .onInstSuccess
  ; Create application data directory
  CreateDirectory "$APPDATA\SuspectsDataManagement"
  CreateDirectory "$APPDATA\SuspectsDataManagement\data"
  CreateDirectory "$APPDATA\SuspectsDataManagement\backups"
  CreateDirectory "$APPDATA\SuspectsDataManagement\logs"
  
  ; Set proper permissions
  AccessControl::GrantOnFile "$APPDATA\SuspectsDataManagement" "(S-1-5-32-545)" "FullAccess"
  
  ; Register file associations (optional)
  WriteRegStr HKCR ".sdm" "" "SuspectsDataManagement.File"
  WriteRegStr HKCR "SuspectsDataManagement.File" "" "Suspects Data Management File"
  WriteRegStr HKCR "SuspectsDataManagement.File\DefaultIcon" "" "$INSTDIR\${PRODUCT_NAME}.exe,0"
  
  ; Add to Windows Firewall exceptions (if needed)
  ; ExecWait 'netsh advfirewall firewall add rule name="Suspects Data Management" dir=in action=allow program="$INSTDIR\${PRODUCT_NAME}.exe"'
FunctionEnd

; Uninstaller
Function un.onInit
  MessageBox MB_YESNO|MB_ICONQUESTION "هل أنت متأكد من إزالة برنامج بيانات المتهمين؟" IDYES +2
  Abort
FunctionEnd

Function un.onUninstSuccess
  ; Remove application data (optional - ask user)
  MessageBox MB_YESNO|MB_ICONQUESTION "هل تريد حذف بيانات البرنامج أيضاً؟$\r$\n(سيتم حذف جميع البيانات والإعدادات)" IDNO +3
  RMDir /r "$APPDATA\SuspectsDataManagement"
  DeleteRegKey HKCU "Software\SuspectsDataManagement"
  
  ; Remove file associations
  DeleteRegKey HKCR ".sdm"
  DeleteRegKey HKCR "SuspectsDataManagement.File"
  
  ; Remove from Windows Firewall
  ; ExecWait 'netsh advfirewall firewall delete rule name="Suspects Data Management"'
  
  MessageBox MB_OK "تم إلغاء تثبيت البرنامج بنجاح."
FunctionEnd

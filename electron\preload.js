const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Activation operations
  saveActivationStatus: (data) => ipcRenderer.invoke('activation:save', data),
  checkActivationStatus: () => ipcRenderer.invoke('activation:check'),
  clearActivationStatus: () => ipcRenderer.invoke('activation:clear'),

  // File operations
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (data) => ipcRenderer.invoke('dialog:saveFile', data),

  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),

  // Update operations
  checkForUpdates: () => ipcRenderer.invoke('updater:checkForUpdates'),
  downloadUpdate: () => ipcRenderer.invoke('updater:downloadUpdate'),
  quitAndInstall: () => ipcRenderer.invoke('updater:quitAndInstall'),
  getUpdateStatus: () => ipcRenderer.invoke('updater:getStatus'),
  onUpdateDownloadProgress: (callback) => ipcRenderer.on('update-download-progress', callback),

  // Window controls
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),

  // Database operations - Suspects
  searchSuspects: (query, options) => ipcRenderer.invoke('db:suspects:search', query, options),
  getSuspects: (page, limit) => ipcRenderer.invoke('db:suspects:get', page, limit),
  addSuspect: (data, userId) => ipcRenderer.invoke('db:suspects:add', data, userId),
  updateSuspect: (id, data, userId) => ipcRenderer.invoke('db:suspects:update', id, data, userId),
  deleteSuspect: (id, userId) => ipcRenderer.invoke('db:suspects:delete', id, userId),

  // Import operations
  importCSV: (filePath, options) => ipcRenderer.invoke('import:csv', filePath, options),
  onImportProgress: (callback) => ipcRenderer.on('import:progress', callback),

  // System info
  getPlatform: () => process.platform,
  getArch: () => process.arch,

  // Event listeners
  onMenuAction: (callback) => ipcRenderer.on('menu:action', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})

// Prevent the renderer process from accessing Node.js
delete window.require
delete window.exports
delete window.module

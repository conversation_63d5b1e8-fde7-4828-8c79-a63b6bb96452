const database = require('./database')
const fs = require('fs')
const path = require('path')
const csv = require('csv-parser')
const Papa = require('papaparse')

class DataService {
  constructor() {
    this.db = null
  }

  // Initialize service
  async initialize() {
    await database.initialize()
    this.db = database.getDatabase()
    console.log('✅ Data service initialized')
  }

  // === SUSPECTS OPERATIONS ===

  // Add suspect with optimized search text
  async addSuspect(data, userId) {
    const searchText = this.generateSearchText(data)
    const stmt = this.db.prepare(`
      INSERT INTO suspects (data, searchText, createdBy, updatedBy)
      VALUES (?, ?, ?, ?)
    `)
    
    const result = stmt.run(JSON.stringify(data), searchText, userId, userId)
    
    // Log audit
    await this.logAudit(userId, 'CREATE', 'suspect', result.lastInsertRowid, { data })
    
    return result.lastInsertRowid
  }

  // Update suspect
  async updateSuspect(id, data, userId) {
    const searchText = this.generateSearchText(data)
    const stmt = this.db.prepare(`
      UPDATE suspects 
      SET data = ?, searchText = ?, updatedBy = ?, updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `)
    
    const result = stmt.run(JSON.stringify(data), searchText, userId, id)
    
    // Log audit
    await this.logAudit(userId, 'UPDATE', 'suspect', id, { data })
    
    return result.changes > 0
  }

  // Delete suspect
  async deleteSuspect(id, userId) {
    const stmt = this.db.prepare('DELETE FROM suspects WHERE id = ?')
    const result = stmt.run(id)
    
    // Log audit
    await this.logAudit(userId, 'DELETE', 'suspect', id)
    
    return result.changes > 0
  }

  // Get suspect by ID
  async getSuspect(id) {
    const stmt = this.db.prepare('SELECT * FROM suspects WHERE id = ?')
    const row = stmt.get(id)
    
    if (row) {
      row.data = JSON.parse(row.data)
    }
    
    return row
  }

  // Get all suspects with pagination
  async getSuspects(page = 1, limit = 100, orderBy = 'createdAt', orderDir = 'DESC') {
    const offset = (page - 1) * limit
    
    const countStmt = this.db.prepare('SELECT COUNT(*) as total FROM suspects')
    const total = countStmt.get().total
    
    const stmt = this.db.prepare(`
      SELECT * FROM suspects 
      ORDER BY ${orderBy} ${orderDir}
      LIMIT ? OFFSET ?
    `)
    
    const rows = stmt.all(limit, offset)
    
    // Parse JSON data
    rows.forEach(row => {
      row.data = JSON.parse(row.data)
    })
    
    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  // Advanced search with FTS
  async searchSuspects(query, options = {}) {
    const {
      page = 1,
      limit = 100,
      useFullText = true,
      exactMatch = false
    } = options

    const offset = (page - 1) * limit
    let stmt, countStmt
    let searchQuery = query.trim()

    if (useFullText && searchQuery) {
      // Use FTS for better performance
      if (!exactMatch) {
        searchQuery = searchQuery.split(' ').map(term => `"${term}"*`).join(' OR ')
      }

      stmt = this.db.prepare(`
        SELECT s.* FROM suspects s
        JOIN suspects_fts fts ON s.id = fts.id
        WHERE suspects_fts MATCH ?
        ORDER BY rank
        LIMIT ? OFFSET ?
      `)

      countStmt = this.db.prepare(`
        SELECT COUNT(*) as total FROM suspects s
        JOIN suspects_fts fts ON s.id = fts.id
        WHERE suspects_fts MATCH ?
      `)
    } else {
      // Fallback to LIKE search
      const likeQuery = `%${searchQuery}%`
      
      stmt = this.db.prepare(`
        SELECT * FROM suspects 
        WHERE searchText LIKE ?
        ORDER BY createdAt DESC
        LIMIT ? OFFSET ?
      `)

      countStmt = this.db.prepare(`
        SELECT COUNT(*) as total FROM suspects 
        WHERE searchText LIKE ?
      `)
    }

    const total = countStmt.get(searchQuery).total
    const rows = stmt.all(searchQuery, limit, offset)

    // Parse JSON data
    rows.forEach(row => {
      row.data = JSON.parse(row.data)
    })

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      query: searchQuery
    }
  }

  // Generate searchable text from suspect data
  generateSearchText(data) {
    const searchableFields = []
    
    // Extract all text values from the data object
    const extractText = (obj, prefix = '') => {
      for (const [key, value] of Object.entries(obj)) {
        if (value && typeof value === 'string') {
          searchableFields.push(value)
        } else if (value && typeof value === 'object' && !Array.isArray(value)) {
          extractText(value, `${prefix}${key}.`)
        } else if (Array.isArray(value)) {
          value.forEach(item => {
            if (typeof item === 'string') {
              searchableFields.push(item)
            } else if (typeof item === 'object') {
              extractText(item, `${prefix}${key}.`)
            }
          })
        }
      }
    }

    extractText(data)
    
    // Join all searchable text with spaces
    return searchableFields.join(' ').toLowerCase()
  }

  // === BULK IMPORT OPERATIONS ===

  // Import CSV with streaming for large files
  async importCSV(filePath, options = {}, progressCallback) {
    return new Promise((resolve, reject) => {
      const results = []
      const errors = []
      let processed = 0
      let imported = 0

      const {
        batchSize = 1000,
        userId = 1,
        skipFirstRow = true
      } = options

      // Get file size for progress calculation
      const stats = fs.statSync(filePath)
      const fileSize = stats.size
      let bytesRead = 0

      const stream = fs.createReadStream(filePath)
        .pipe(csv({
          skipEmptyLines: true,
          skipLinesWithError: true
        }))

      const batch = []

      stream.on('data', (row) => {
        processed++
        bytesRead += JSON.stringify(row).length

        // Skip first row if needed
        if (skipFirstRow && processed === 1) {
          return
        }

        batch.push(row)

        // Process batch when it reaches batchSize
        if (batch.length >= batchSize) {
          this.processBatch(batch, userId)
          imported += batch.length
          batch.length = 0 // Clear batch

          // Report progress
          if (progressCallback) {
            const progress = Math.round((bytesRead / fileSize) * 100)
            progressCallback({
              processed,
              imported,
              progress,
              errors: errors.length
            })
          }
        }
      })

      stream.on('end', () => {
        // Process remaining batch
        if (batch.length > 0) {
          this.processBatch(batch, userId)
          imported += batch.length
        }

        resolve({
          processed,
          imported,
          errors,
          success: true
        })
      })

      stream.on('error', (error) => {
        errors.push({ error: error.message })
        reject(error)
      })
    })
  }

  // Process batch of records
  processBatch(batch, userId) {
    const transaction = this.db.transaction((records) => {
      const stmt = this.db.prepare(`
        INSERT INTO suspects (data, searchText, createdBy, updatedBy)
        VALUES (?, ?, ?, ?)
      `)

      for (const record of records) {
        try {
          const searchText = this.generateSearchText(record)
          stmt.run(JSON.stringify(record), searchText, userId, userId)
        } catch (error) {
          console.error('Error inserting record:', error)
        }
      }
    })

    transaction(batch)
  }

  // === ACTIVATION OPERATIONS ===

  // Save activation status
  async saveActivationStatus(data) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO activation_status (id, activated, activationDate, timestamp)
      VALUES (1, ?, ?, ?)
    `)
    
    return stmt.run(data.activated ? 1 : 0, data.date, data.timestamp)
  }

  // Check activation status
  async checkActivationStatus() {
    const stmt = this.db.prepare('SELECT * FROM activation_status WHERE id = 1')
    const row = stmt.get()
    
    if (row) {
      return {
        activated: row.activated === 1,
        date: row.activationDate,
        timestamp: row.timestamp
      }
    }
    
    return null
  }

  // Clear activation status
  async clearActivationStatus() {
    const stmt = this.db.prepare('DELETE FROM activation_status WHERE id = 1')
    return stmt.run()
  }

  // === AUDIT LOG ===

  // Log audit entry
  async logAudit(userId, action, resource, resourceId = null, details = null) {
    const stmt = this.db.prepare(`
      INSERT INTO audit_log (userId, action, resource, resourceId, details)
      VALUES (?, ?, ?, ?, ?)
    `)
    
    return stmt.run(
      userId,
      action,
      resource,
      resourceId,
      details ? JSON.stringify(details) : null
    )
  }

  // Get audit logs
  async getAuditLogs(page = 1, limit = 100) {
    const offset = (page - 1) * limit
    
    const countStmt = this.db.prepare('SELECT COUNT(*) as total FROM audit_log')
    const total = countStmt.get().total
    
    const stmt = this.db.prepare(`
      SELECT a.*, u.username, u.fullName
      FROM audit_log a
      LEFT JOIN users u ON a.userId = u.id
      ORDER BY a.timestamp DESC
      LIMIT ? OFFSET ?
    `)
    
    const rows = stmt.all(limit, offset)
    
    // Parse JSON details
    rows.forEach(row => {
      if (row.details) {
        try {
          row.details = JSON.parse(row.details)
        } catch (e) {
          // Keep as string if parsing fails
        }
      }
    })
    
    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  // === UTILITY METHODS ===

  // Get database statistics
  async getStats() {
    const stats = {}
    
    const tables = ['users', 'suspects', 'fields', 'attachments', 'audit_log']
    
    for (const table of tables) {
      const stmt = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`)
      stats[table] = stmt.get().count
    }
    
    return stats
  }

  // Optimize database
  async optimize() {
    this.db.exec('VACUUM')
    this.db.exec('ANALYZE')
    console.log('✅ Database optimized')
  }
}

module.exports = new DataService()

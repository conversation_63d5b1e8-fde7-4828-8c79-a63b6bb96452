import type { SuspectData } from '@/types'

export interface SearchOptions {
  query: string
  page?: number
  limit?: number
  useFullText?: boolean
  exactMatch?: boolean
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export interface SearchResult {
  data: SuspectData[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  query: string
  executionTime: number
}

export interface ImportProgress {
  processed: number
  imported: number
  progress: number
  errors: number
  currentBatch?: number
  totalBatches?: number
  estimatedTimeRemaining?: number
}

export interface ImportResult {
  success: boolean
  processed: number
  imported: number
  errors: any[]
  executionTime: number
}

class ElectronSearchService {
  private isElectron: boolean
  private searchCache: Map<string, SearchResult> = new Map()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.isElectron = !!(window as any).electronAPI
    console.log('🔍 ElectronSearchService initialized', { isElectron: this.isElectron })
  }

  // Enhanced search with caching and performance optimization
  async searchSuspects(options: SearchOptions): Promise<SearchResult> {
    const startTime = performance.now()
    
    try {
      // Create cache key
      const cacheKey = this.createCacheKey(options)
      
      // Check cache first
      if (this.searchCache.has(cacheKey)) {
        const cached = this.searchCache.get(cacheKey)!
        console.log('🎯 Search result from cache', { query: options.query, results: cached.data.length })
        return {
          ...cached,
          executionTime: performance.now() - startTime
        }
      }

      let result: SearchResult

      if (this.isElectron && window.electronAPI) {
        // Use Electron's optimized search
        result = await this.electronSearch(options)
      } else {
        // Fallback to IndexedDB search for web version
        result = await this.fallbackSearch(options)
      }

      // Cache the result
      this.cacheResult(cacheKey, result)

      result.executionTime = performance.now() - startTime
      
      console.log('🔍 Search completed', {
        query: options.query,
        results: result.data.length,
        total: result.pagination.total,
        executionTime: result.executionTime
      })

      return result
    } catch (error) {
      console.error('❌ Search failed:', error)
      throw error
    }
  }

  // Electron-optimized search using SQLite FTS
  private async electronSearch(options: SearchOptions): Promise<SearchResult> {
    const {
      query,
      page = 1,
      limit = 100,
      useFullText = true,
      exactMatch = false
    } = options

    const searchOptions = {
      page,
      limit,
      useFullText,
      exactMatch
    }

    const result = await window.electronAPI.searchSuspects(query, searchOptions)
    
    return {
      data: result.data || [],
      pagination: result.pagination || {
        page: 1,
        limit: 100,
        total: 0,
        totalPages: 0
      },
      query: result.query || query,
      executionTime: 0 // Will be set by caller
    }
  }

  // Fallback search for web version (using existing IndexedDB)
  private async fallbackSearch(options: SearchOptions): Promise<SearchResult> {
    // This would use the existing Dexie-based search
    // For now, return empty result as placeholder
    console.warn('⚠️ Using fallback search - limited performance with large datasets')
    
    return {
      data: [],
      pagination: {
        page: options.page || 1,
        limit: options.limit || 100,
        total: 0,
        totalPages: 0
      },
      query: options.query,
      executionTime: 0
    }
  }

  // Get all suspects with pagination
  async getSuspects(page = 1, limit = 100): Promise<SearchResult> {
    const startTime = performance.now()

    try {
      if (this.isElectron && window.electronAPI) {
        const result = await window.electronAPI.getSuspects(page, limit)
        
        return {
          data: result.data || [],
          pagination: result.pagination || {
            page: 1,
            limit: 100,
            total: 0,
            totalPages: 0
          },
          query: '',
          executionTime: performance.now() - startTime
        }
      } else {
        // Fallback for web version
        return this.fallbackSearch({ query: '', page, limit })
      }
    } catch (error) {
      console.error('❌ Failed to get suspects:', error)
      throw error
    }
  }

  // Add suspect
  async addSuspect(data: any, userId: number): Promise<number> {
    if (this.isElectron && window.electronAPI) {
      const result = await window.electronAPI.addSuspect(data, userId)
      this.clearCache() // Clear cache after data modification
      return result
    } else {
      throw new Error('Add suspect only available in Electron version')
    }
  }

  // Update suspect
  async updateSuspect(id: number, data: any, userId: number): Promise<boolean> {
    if (this.isElectron && window.electronAPI) {
      const result = await window.electronAPI.updateSuspect(id, data, userId)
      this.clearCache() // Clear cache after data modification
      return result
    } else {
      throw new Error('Update suspect only available in Electron version')
    }
  }

  // Delete suspect
  async deleteSuspect(id: number, userId: number): Promise<boolean> {
    if (this.isElectron && window.electronAPI) {
      const result = await window.electronAPI.deleteSuspect(id, userId)
      this.clearCache() // Clear cache after data modification
      return result
    } else {
      throw new Error('Delete suspect only available in Electron version')
    }
  }

  // Enhanced CSV import with progress tracking
  async importCSV(
    filePath: string, 
    options: any = {}, 
    progressCallback?: (progress: ImportProgress) => void
  ): Promise<ImportResult> {
    const startTime = performance.now()

    if (!this.isElectron || !window.electronAPI) {
      throw new Error('CSV import only available in Electron version')
    }

    try {
      // Setup progress listener
      if (progressCallback) {
        window.electronAPI.onImportProgress((event: any, progress: ImportProgress) => {
          // Add estimated time remaining
          if (progress.progress > 0) {
            const elapsed = performance.now() - startTime
            const estimatedTotal = (elapsed / progress.progress) * 100
            progress.estimatedTimeRemaining = Math.max(0, estimatedTotal - elapsed)
          }
          
          progressCallback(progress)
        })
      }

      // Start import
      const result = await window.electronAPI.importCSV(filePath, {
        batchSize: 1000,
        userId: 1,
        skipFirstRow: true,
        ...options
      })

      // Clear cache after import
      this.clearCache()

      return {
        ...result,
        executionTime: performance.now() - startTime
      }
    } catch (error) {
      console.error('❌ CSV import failed:', error)
      throw error
    } finally {
      // Clean up progress listener
      if (window.electronAPI.removeAllListeners) {
        window.electronAPI.removeAllListeners('import:progress')
      }
    }
  }

  // Cache management
  private createCacheKey(options: SearchOptions): string {
    return JSON.stringify({
      query: options.query,
      page: options.page,
      limit: options.limit,
      useFullText: options.useFullText,
      exactMatch: options.exactMatch
    })
  }

  private cacheResult(key: string, result: SearchResult): void {
    this.searchCache.set(key, result)
    
    // Auto-clear cache after timeout
    setTimeout(() => {
      this.searchCache.delete(key)
    }, this.cacheTimeout)
  }

  private clearCache(): void {
    this.searchCache.clear()
    console.log('🗑️ Search cache cleared')
  }

  // Performance monitoring
  getPerformanceStats() {
    return {
      isElectron: this.isElectron,
      cacheSize: this.searchCache.size,
      cacheTimeout: this.cacheTimeout
    }
  }

  // Cleanup
  destroy(): void {
    this.clearCache()
    if (this.isElectron && window.electronAPI?.removeAllListeners) {
      window.electronAPI.removeAllListeners('import:progress')
    }
  }
}

// Export singleton instance
export const electronSearchService = new ElectronSearchService()

// Type declarations for global electronAPI
declare global {
  interface Window {
    electronAPI?: {
      searchSuspects: (query: string, options: any) => Promise<any>
      getSuspects: (page: number, limit: number) => Promise<any>
      addSuspect: (data: any, userId: number) => Promise<number>
      updateSuspect: (id: number, data: any, userId: number) => Promise<boolean>
      deleteSuspect: (id: number, userId: number) => Promise<boolean>
      importCSV: (filePath: string, options: any) => Promise<any>
      onImportProgress: (callback: (event: any, progress: ImportProgress) => void) => void
      removeAllListeners: (channel: string) => void
    }
  }
}

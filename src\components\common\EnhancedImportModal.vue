<template>
  <div 
    v-if="showModal" 
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
  >
    <div class="neumorphic-card bg-white p-8 rounded-2xl shadow-2xl max-w-2xl w-full mx-4 transform transition-all duration-300">
      <!-- Header -->
      <div class="text-center mb-6">
        <div class="neumorphic-icon mx-auto mb-4 w-16 h-16 flex items-center justify-center">
          <i class="fas fa-file-import text-primary-600 text-2xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-secondary-800 mb-2">استيراد البيانات المحسن</h2>
        <p class="text-secondary-600">دعم كامل للملفات الكبيرة مع تتبع التقدم المباشر</p>
      </div>

      <!-- File Selection -->
      <div v-if="!isImporting && !importCompleted" class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            اختيار الملف
          </label>
          <div class="flex items-center gap-4">
            <button
              @click="selectFile"
              class="neumorphic-button px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <i class="fas fa-folder-open mr-2"></i>
              اختيار ملف CSV/Excel
            </button>
            <span v-if="selectedFile" class="text-sm text-secondary-600">
              {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
            </span>
          </div>
        </div>

        <!-- Import Options -->
        <div v-if="selectedFile" class="space-y-4">
          <h3 class="text-lg font-medium text-secondary-800">خيارات الاستيراد</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-1">
                حجم الدفعة
              </label>
              <select v-model="importOptions.batchSize" class="neumorphic-input w-full">
                <option value="500">500 سجل</option>
                <option value="1000">1000 سجل</option>
                <option value="2000">2000 سجل</option>
                <option value="5000">5000 سجل</option>
              </select>
            </div>
            
            <div>
              <label class="flex items-center">
                <input 
                  v-model="importOptions.skipFirstRow" 
                  type="checkbox" 
                  class="mr-2"
                >
                <span class="text-sm text-secondary-700">تخطي الصف الأول (العناوين)</span>
              </label>
            </div>
          </div>

          <!-- Performance Warning for Large Files -->
          <div v-if="selectedFile.size > 50 * 1024 * 1024" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
              <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-3"></i>
              <div>
                <h4 class="font-medium text-yellow-800">ملف كبير الحجم</h4>
                <p class="text-sm text-yellow-700 mt-1">
                  هذا الملف كبير الحجم ({{ formatFileSize(selectedFile.size) }}). 
                  سيتم استخدام المعالجة المحسنة للبيانات الضخمة.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end gap-4 pt-4">
          <button
            @click="closeModal"
            class="neumorphic-button px-6 py-3 text-secondary-600 hover:bg-secondary-50 transition-colors"
          >
            إلغاء
          </button>
          <button
            @click="startImport"
            :disabled="!selectedFile"
            class="neumorphic-button px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <i class="fas fa-upload mr-2"></i>
            بدء الاستيراد
          </button>
        </div>
      </div>

      <!-- Import Progress -->
      <div v-if="isImporting" class="space-y-6">
        <div class="text-center">
          <h3 class="text-lg font-medium text-secondary-800 mb-2">جاري استيراد البيانات...</h3>
          <p class="text-sm text-secondary-600">يرجى عدم إغلاق النافذة أثناء عملية الاستيراد</p>
        </div>

        <!-- Progress Bar -->
        <div class="space-y-2">
          <div class="flex justify-between text-sm text-secondary-600">
            <span>التقدم: {{ importProgress.progress }}%</span>
            <span>{{ importProgress.processed }} / {{ estimatedTotal }} سجل</span>
          </div>
          <div class="w-full bg-secondary-200 rounded-full h-3 overflow-hidden">
            <div 
              class="bg-gradient-to-r from-primary-500 to-primary-600 h-full rounded-full transition-all duration-300 ease-out"
              :style="{ width: `${importProgress.progress}%` }"
            ></div>
          </div>
        </div>

        <!-- Detailed Stats -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="bg-green-50 p-3 rounded-lg">
            <div class="text-green-800 font-medium">تم الاستيراد</div>
            <div class="text-green-600">{{ importProgress.imported }} سجل</div>
          </div>
          <div class="bg-red-50 p-3 rounded-lg">
            <div class="text-red-800 font-medium">أخطاء</div>
            <div class="text-red-600">{{ importProgress.errors }} خطأ</div>
          </div>
        </div>

        <!-- Time Estimation -->
        <div v-if="importProgress.estimatedTimeRemaining" class="text-center text-sm text-secondary-600">
          الوقت المتبقي المقدر: {{ formatTime(importProgress.estimatedTimeRemaining) }}
        </div>

        <!-- Cancel Button -->
        <div class="text-center">
          <button
            @click="cancelImport"
            class="neumorphic-button px-6 py-2 text-red-600 hover:bg-red-50 transition-colors"
          >
            <i class="fas fa-times mr-2"></i>
            إلغاء العملية
          </button>
        </div>
      </div>

      <!-- Import Completed -->
      <div v-if="importCompleted" class="space-y-6 text-center">
        <div class="neumorphic-icon mx-auto w-16 h-16 flex items-center justify-center bg-green-50">
          <i class="fas fa-check-circle text-green-600 text-2xl"></i>
        </div>
        
        <div>
          <h3 class="text-lg font-medium text-secondary-800 mb-2">تم الاستيراد بنجاح!</h3>
          <p class="text-secondary-600">تم استيراد البيانات وحفظها في قاعدة البيانات</p>
        </div>

        <!-- Results Summary -->
        <div class="bg-secondary-50 p-4 rounded-lg">
          <div class="grid grid-cols-3 gap-4 text-sm">
            <div>
              <div class="font-medium text-secondary-800">إجمالي المعالج</div>
              <div class="text-secondary-600">{{ importResult?.processed || 0 }}</div>
            </div>
            <div>
              <div class="font-medium text-secondary-800">تم الاستيراد</div>
              <div class="text-green-600">{{ importResult?.imported || 0 }}</div>
            </div>
            <div>
              <div class="font-medium text-secondary-800">وقت التنفيذ</div>
              <div class="text-secondary-600">{{ formatTime(importResult?.executionTime || 0) }}</div>
            </div>
          </div>
        </div>

        <!-- Close Button -->
        <button
          @click="closeModal"
          class="neumorphic-button px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <i class="fas fa-check mr-2"></i>
          إغلاق
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { electronSearchService } from '@/services/ElectronSearchService'
import type { ImportProgress, ImportResult } from '@/services/ElectronSearchService'

// Props & Emits
const emit = defineEmits<{
  close: []
  completed: [result: ImportResult]
}>()

// Reactive data
const showModal = ref(true)
const selectedFile = ref<File | null>(null)
const isImporting = ref(false)
const importCompleted = ref(false)
const importProgress = ref<ImportProgress>({
  processed: 0,
  imported: 0,
  progress: 0,
  errors: 0
})
const importResult = ref<ImportResult | null>(null)

const importOptions = ref({
  batchSize: 1000,
  skipFirstRow: true,
  userId: 1
})

// Computed
const estimatedTotal = computed(() => {
  if (!selectedFile.value) return 0
  // Rough estimation: 100 bytes per record on average
  return Math.floor(selectedFile.value.size / 100)
})

// Methods
const selectFile = async () => {
  try {
    if (window.electronAPI?.openFile) {
      const result = await window.electronAPI.openFile()
      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0]
        // Create a mock File object for display
        selectedFile.value = {
          name: filePath.split('\\').pop() || filePath.split('/').pop() || 'Unknown',
          size: 0, // Will be determined by the backend
          path: filePath
        } as any
      }
    }
  } catch (error) {
    console.error('Error selecting file:', error)
  }
}

const startImport = async () => {
  if (!selectedFile.value) return

  isImporting.value = true
  importProgress.value = {
    processed: 0,
    imported: 0,
    progress: 0,
    errors: 0
  }

  try {
    const result = await electronSearchService.importCSV(
      (selectedFile.value as any).path,
      importOptions.value,
      (progress: ImportProgress) => {
        importProgress.value = progress
      }
    )

    importResult.value = result
    importCompleted.value = true
    emit('completed', result)
  } catch (error) {
    console.error('Import failed:', error)
    // Handle error
  } finally {
    isImporting.value = false
  }
}

const cancelImport = () => {
  // Implementation for canceling import
  isImporting.value = false
  importProgress.value = {
    processed: 0,
    imported: 0,
    progress: 0,
    errors: 0
  }
}

const closeModal = () => {
  showModal.value = false
  emit('close')
}

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (ms: number): string => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}س ${minutes % 60}د`
  } else if (minutes > 0) {
    return `${minutes}د ${seconds % 60}ث`
  } else {
    return `${seconds}ث`
  }
}
</script>

<style scoped>
.neumorphic-card {
  box-shadow: 
    20px 20px 60px #d1d9e6,
    -20px -20px 60px #ffffff;
}

.neumorphic-icon {
  background: linear-gradient(145deg, #f0f0f0, #cacaca);
  box-shadow: 
    5px 5px 10px #d1d9e6,
    -5px -5px 10px #ffffff;
}

.neumorphic-input {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  box-shadow: 
    inset 5px 5px 10px #d1d9e6,
    inset -5px -5px 10px #ffffff;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.neumorphic-button {
  box-shadow: 
    5px 5px 10px #d1d9e6,
    -5px -5px 10px #ffffff;
  border-radius: 0.5rem;
}

.neumorphic-button:hover:not(:disabled) {
  box-shadow: 
    3px 3px 6px #d1d9e6,
    -3px -3px 6px #ffffff;
}

.neumorphic-button:active:not(:disabled) {
  box-shadow: 
    inset 3px 3px 6px #d1d9e6,
    inset -3px -3px 6px #ffffff;
}
</style>

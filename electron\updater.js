const { autoUpdater } = require('electron-updater')
const { dialog, BrowserWindow } = require('electron')
const isDev = process.env.NODE_ENV === 'development'

class AppUpdater {
  constructor() {
    this.mainWindow = null
    this.updateAvailable = false
    this.updateDownloaded = false
    
    // Configure auto-updater
    this.configureUpdater()
    
    // Setup event listeners
    this.setupEventListeners()
  }

  // Configure updater settings
  configureUpdater() {
    // Disable auto-download in development
    autoUpdater.autoDownload = !isDev
    autoUpdater.autoInstallOnAppQuit = true
    
    // Configure update server (GitHub releases)
    if (!isDev) {
      autoUpdater.setFeedURL({
        provider: 'github',
        owner: 'your-github-username',
        repo: 'suspects-data-management',
        private: true,
        token: process.env.GITHUB_TOKEN // Set this in your environment
      })
    }

    // Logging
    autoUpdater.logger = require('electron-log')
    autoUpdater.logger.transports.file.level = 'info'
    
    console.log('✅ Auto-updater configured')
  }

  // Setup event listeners
  setupEventListeners() {
    // Update available
    autoUpdater.on('update-available', (info) => {
      console.log('🔄 Update available:', info.version)
      this.updateAvailable = true
      
      if (this.mainWindow) {
        this.showUpdateAvailableDialog(info)
      }
    })

    // Update not available
    autoUpdater.on('update-not-available', (info) => {
      console.log('✅ App is up to date:', info.version)
      this.updateAvailable = false
    })

    // Update error
    autoUpdater.on('error', (error) => {
      console.error('❌ Update error:', error)
      
      if (this.mainWindow) {
        this.showUpdateErrorDialog(error)
      }
    })

    // Download progress
    autoUpdater.on('download-progress', (progressObj) => {
      const logMessage = `Download speed: ${progressObj.bytesPerSecond} - Downloaded ${progressObj.percent}% (${progressObj.transferred}/${progressObj.total})`
      console.log('📥 Download progress:', logMessage)
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send('update-download-progress', progressObj)
      }
    })

    // Update downloaded
    autoUpdater.on('update-downloaded', (info) => {
      console.log('✅ Update downloaded:', info.version)
      this.updateDownloaded = true
      
      if (this.mainWindow) {
        this.showUpdateDownloadedDialog(info)
      }
    })
  }

  // Set main window reference
  setMainWindow(window) {
    this.mainWindow = window
  }

  // Check for updates
  async checkForUpdates() {
    if (isDev) {
      console.log('⚠️ Skipping update check in development mode')
      return
    }

    try {
      console.log('🔍 Checking for updates...')
      const result = await autoUpdater.checkForUpdatesAndNotify()
      
      if (result) {
        console.log('📋 Update check result:', {
          updateInfo: result.updateInfo.version,
          downloadPromise: !!result.downloadPromise
        })
      }
      
      return result
    } catch (error) {
      console.error('❌ Failed to check for updates:', error)
      throw error
    }
  }

  // Download update manually
  async downloadUpdate() {
    if (!this.updateAvailable) {
      throw new Error('No update available to download')
    }

    try {
      console.log('📥 Starting update download...')
      await autoUpdater.downloadUpdate()
    } catch (error) {
      console.error('❌ Failed to download update:', error)
      throw error
    }
  }

  // Install update and restart
  quitAndInstall() {
    if (!this.updateDownloaded) {
      throw new Error('No update downloaded to install')
    }

    console.log('🔄 Installing update and restarting...')
    autoUpdater.quitAndInstall()
  }

  // Show update available dialog
  showUpdateAvailableDialog(info) {
    const options = {
      type: 'info',
      title: 'تحديث متاح',
      message: `إصدار جديد متاح (${info.version})`,
      detail: 'هل تريد تحميل وتثبيت التحديث الآن؟',
      buttons: ['تحميل الآن', 'تذكيرني لاحقاً', 'تخطي هذا الإصدار'],
      defaultId: 0,
      cancelId: 1
    }

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // Download now
        this.downloadUpdate().catch(console.error)
      } else if (result.response === 2) {
        // Skip this version
        console.log('⏭️ User chose to skip version:', info.version)
      }
    })
  }

  // Show update downloaded dialog
  showUpdateDownloadedDialog(info) {
    const options = {
      type: 'info',
      title: 'التحديث جاهز',
      message: `تم تحميل الإصدار ${info.version} بنجاح`,
      detail: 'سيتم إعادة تشغيل التطبيق لتطبيق التحديث. هل تريد المتابعة الآن؟',
      buttons: ['إعادة التشغيل الآن', 'إعادة التشغيل لاحقاً'],
      defaultId: 0,
      cancelId: 1
    }

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // Restart now
        this.quitAndInstall()
      } else {
        console.log('⏰ User chose to restart later')
      }
    })
  }

  // Show update error dialog
  showUpdateErrorDialog(error) {
    const options = {
      type: 'error',
      title: 'خطأ في التحديث',
      message: 'حدث خطأ أثناء البحث عن التحديثات',
      detail: `تفاصيل الخطأ: ${error.message}`,
      buttons: ['موافق', 'إعادة المحاولة'],
      defaultId: 0,
      cancelId: 0
    }

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 1) {
        // Retry
        setTimeout(() => {
          this.checkForUpdates().catch(console.error)
        }, 5000)
      }
    })
  }

  // Manual update check (called from menu or UI)
  async manualUpdateCheck() {
    try {
      const result = await this.checkForUpdates()
      
      if (!result || !this.updateAvailable) {
        // Show "no updates" dialog
        const options = {
          type: 'info',
          title: 'لا توجد تحديثات',
          message: 'التطبيق محدث إلى أحدث إصدار',
          detail: 'لا توجد تحديثات متاحة في الوقت الحالي.',
          buttons: ['موافق'],
          defaultId: 0
        }

        if (this.mainWindow) {
          await dialog.showMessageBox(this.mainWindow, options)
        }
      }
    } catch (error) {
      console.error('❌ Manual update check failed:', error)
      this.showUpdateErrorDialog(error)
    }
  }

  // Get update status
  getUpdateStatus() {
    return {
      updateAvailable: this.updateAvailable,
      updateDownloaded: this.updateDownloaded,
      isDev
    }
  }

  // Schedule automatic update checks
  scheduleUpdateChecks() {
    if (isDev) {
      console.log('⚠️ Skipping scheduled update checks in development mode')
      return
    }

    // Check for updates every 4 hours
    const checkInterval = 4 * 60 * 60 * 1000 // 4 hours in milliseconds
    
    setInterval(() => {
      console.log('⏰ Scheduled update check...')
      this.checkForUpdates().catch(console.error)
    }, checkInterval)

    console.log('⏰ Scheduled update checks every 4 hours')
  }
}

// Export singleton instance
const appUpdater = new AppUpdater()
module.exports = appUpdater

<template>
  <div 
    v-if="showModal" 
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
    @click.self="preventClose"
  >
    <div class="neumorphic-card bg-white p-8 rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300">
      <!-- Header -->
      <div class="text-center mb-6">
        <div class="neumorphic-icon mx-auto mb-4 w-16 h-16 flex items-center justify-center">
          <i class="fas fa-key text-primary-600 text-2xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-secondary-800 mb-2">تفعيل البرنامج</h2>
        <p class="text-secondary-600">يرجى إدخال رمز التفعيل للمتابعة</p>
      </div>

      <!-- Activation Form -->
      <form @submit.prevent="handleActivation" class="space-y-6">
        <!-- Activation Code Input -->
        <div>
          <label for="activationCode" class="block text-sm font-medium text-secondary-700 mb-2">
            رمز التفعيل
          </label>
          <input
            id="activationCode"
            v-model="activationCode"
            type="text"
            class="neumorphic-input w-full px-4 py-3 text-center text-lg font-mono tracking-wider"
            placeholder="أدخل رمز التفعيل"
            :class="{ 'border-red-300 bg-red-50': hasError }"
            autocomplete="off"
            spellcheck="false"
            @input="clearError"
          />
        </div>

        <!-- Error Message -->
        <div v-if="hasError" class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-lg">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          {{ errorMessage }}
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="text-center py-4">
          <div class="inline-flex items-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mr-3"></div>
            <span class="text-secondary-600">جاري التحقق من الرمز...</span>
          </div>
        </div>

        <!-- Activation Button -->
        <button
          type="submit"
          :disabled="!activationCode.trim() || isLoading"
          class="neumorphic-button w-full py-3 px-6 bg-primary-600 text-white font-medium rounded-lg transition-all duration-300 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <i class="fas fa-unlock mr-2"></i>
          تفعيل البرنامج
        </button>
      </form>

      <!-- Help Text -->
      <div class="mt-6 text-center">
        <p class="text-xs text-secondary-500">
          إذا كنت لا تملك رمز التفعيل، يرجى التواصل مع المطور
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useActivationStore } from '@/stores/activation'

// Store
const activationStore = useActivationStore()

// Reactive data
const showModal = ref(true)
const activationCode = ref('')
const hasError = ref(false)
const errorMessage = ref('')
const isLoading = ref(false)

// Methods
const handleActivation = async () => {
  if (!activationCode.value.trim()) {
    showError('يرجى إدخال رمز التفعيل')
    return
  }

  isLoading.value = true
  hasError.value = false

  try {
    const isValid = await activationStore.validateActivationCode(activationCode.value.trim())
    
    if (isValid) {
      // Success - save activation status and close modal
      await activationStore.saveActivationStatus()
      showModal.value = false
      
      // Emit success event to parent
      emit('activated')
    } else {
      showError('رمز التفعيل غير صحيح، يرجى المحاولة مرة أخرى')
      activationCode.value = ''
    }
  } catch (error) {
    console.error('Activation error:', error)
    showError('حدث خطأ أثناء التحقق من رمز التفعيل')
  } finally {
    isLoading.value = false
  }
}

const showError = (message: string) => {
  hasError.value = true
  errorMessage.value = message
}

const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

const preventClose = () => {
  // Prevent closing modal by clicking outside
  return false
}

// Emits
const emit = defineEmits<{
  activated: []
}>()

// Check if already activated on mount
onMounted(async () => {
  const isActivated = await activationStore.checkActivationStatus()
  if (isActivated) {
    showModal.value = false
    emit('activated')
  }
})
</script>

<style scoped>
/* Additional styles for activation modal */
.neumorphic-card {
  box-shadow: 
    20px 20px 60px #d1d9e6,
    -20px -20px 60px #ffffff;
}

.neumorphic-icon {
  background: linear-gradient(145deg, #f0f0f0, #cacaca);
  box-shadow: 
    5px 5px 10px #d1d9e6,
    -5px -5px 10px #ffffff;
}

.neumorphic-input {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  box-shadow: 
    inset 5px 5px 10px #d1d9e6,
    inset -5px -5px 10px #ffffff;
}

.neumorphic-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 
    inset 5px 5px 10px #d1d9e6,
    inset -5px -5px 10px #ffffff,
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

.neumorphic-button {
  box-shadow: 
    5px 5px 10px #d1d9e6,
    -5px -5px 10px #ffffff;
}

.neumorphic-button:hover:not(:disabled) {
  box-shadow: 
    3px 3px 6px #d1d9e6,
    -3px -3px 6px #ffffff;
}

.neumorphic-button:active:not(:disabled) {
  box-shadow: 
    inset 3px 3px 6px #d1d9e6,
    inset -3px -3px 6px #ffffff;
}
</style>

# برنامج بيانات المتهمين - Desktop Application

نظام متكامل ومتطور لإدارة بيانات المتهمين مبني بتقنيات حديثة مع دعم كامل للبيانات الضخمة والبحث السريع.

## 🌟 المميزات الجديدة في الإصدار المكتبي

### ✨ نظام التفعيل المحلي
- شاشة تفعيل آمنة عند بدء التشغيل
- ثلاثة أكواد تفعيل صالحة: `773$729#886`, `777$3236#888`, `777$167#794`
- حفظ حالة التفعيل محلياً مع التشفير
- تخطي شاشة التفعيل بعد التفعيل الأول

### 🚀 أداء محسن للبيانات الضخمة
- قاعدة بيانات SQLite مع فهارس محسنة
- بحث نصي كامل (Full-Text Search) باستخدام FTS5
- استيراد الملفات الكبيرة (حتى مليون سجل) بتقنية Streaming
- معالجة دفعية للبيانات (Batch Processing)
- تتبع التقدم المباشر أثناء الاستيراد

### 🔍 بحث متقدم وسريع
- بحث فوري في البيانات الضخمة
- دعم البحث النصي المعقد
- فلترة وترتيب متقدم
- ذاكرة تخزين مؤقت للنتائج (Caching)

### 🔐 أمان وحماية متقدمة
- تشفير البيانات الحساسة
- سياسات أمان محتوى صارمة (CSP)
- توقيع رقمي للملف التنفيذي
- حماية من البرمجيات الخبيثة

### 🔄 تحديث تلقائي
- فحص التحديثات تلقائياً كل 4 ساعات
- تحميل وتثبيت التحديثات بأمان
- إشعارات التحديث باللغة العربية

## 🛠️ التقنيات المستخدمة

- **Frontend**: Vue.js 3 + TypeScript + Vite
- **Desktop**: Electron 28
- **Database**: SQLite + better-sqlite3
- **Styling**: Tailwind CSS + Neumorphic Design
- **State Management**: Pinia
- **Security**: Content Security Policy + Code Signing
- **Updates**: electron-updater
- **Build**: electron-builder

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 7 SP1 أو أحدث
- **المعالج**: Intel/AMD 64-bit
- **الذاكرة**: 4 GB RAM
- **التخزين**: 500 MB مساحة فارغة

### الموصى به
- **نظام التشغيل**: Windows 10/11
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM أو أكثر
- **التخزين**: 2 GB مساحة فارغة (SSD مفضل)

## 🚀 التثبيت والتشغيل

### للمطورين - وضع التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-username/suspects-data-management.git
cd suspects-data-management

# تثبيت التبعيات
npm install

# تشغيل وضع التطوير
npm run dev

# في terminal آخر - تشغيل Electron
npm run electron-dev
```

### للمستخدمين النهائيين

1. تحميل ملف التثبيت من صفحة الإصدارات
2. تشغيل ملف `.exe` كمدير
3. اتباع تعليمات التثبيت
4. تشغيل البرنامج من سطح المكتب

## 🔑 نظام التفعيل

عند تشغيل البرنامج لأول مرة، ستظهر شاشة التفعيل. استخدم أحد الأكواد التالية:

- `773$729#886`
- `777$3236#888`
- `777$167#794`

> **ملاحظة**: بعد التفعيل الأول، لن تظهر شاشة التفعيل مرة أخرى.

## 📊 استيراد البيانات الضخمة

البرنامج يدعم استيراد ملفات CSV و Excel كبيرة الحجم بكفاءة عالية:

### المميزات
- **معالجة دفعية**: يتم معالجة البيانات على دفعات لتجنب استهلاك الذاكرة
- **تتبع التقدم**: شريط تقدم مباشر مع تقدير الوقت المتبقي
- **معالجة الأخطاء**: تسجيل وعرض الأخطاء مع إمكانية المتابعة

### خطوات الاستيراد
1. اختر "استيراد البيانات" من القائمة
2. حدد ملف CSV أو Excel
3. اختر خيارات الاستيراد (حجم الدفعة، تخطي الصف الأول)
4. انقر "بدء الاستيراد"
5. انتظر حتى اكتمال العملية

## 🔧 البناء والتوزيع

### بناء النسخة النهائية

```bash
# بناء الواجهة الأمامية
npm run build

# بناء تطبيق Electron
npm run build-electron

# إنشاء ملف التثبيت
npm run dist
```

### إعداد التوقيع الرقمي

```bash
# اضبط متغيرات البيئة
set WINDOWS_CERTIFICATE_FILE=path/to/certificate.p12
set WINDOWS_CERTIFICATE_PASSWORD=your_password

# قم بالبناء
npm run dist
```

## 🔄 نظام التحديث

### للمطورين
1. قم برفع إصدار جديد على GitHub
2. اضبط متغير البيئة `GITHUB_TOKEN`
3. البرنامج سيتحقق من التحديثات تلقائياً

### للمستخدمين
- التحقق التلقائي كل 4 ساعات
- إشعار عند توفر تحديث
- تحميل وتثبيت آمن

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. البرنامج لا يبدأ**
- تأكد من تشغيله كمدير
- تحقق من توافق نظام التشغيل
- أعد تثبيت Visual C++ Redistributable

**2. بطء في البحث**
- تأكد من وجود مساحة كافية على القرص
- أعد بناء فهارس قاعدة البيانات من الإعدادات

**3. فشل في استيراد الملفات**
- تحقق من صيغة الملف (CSV أو Excel)
- تأكد من وجود صلاحيات الكتابة
- قلل من حجم الدفعة في خيارات الاستيراد

## 📞 الدعم والمساعدة

- **GitHub Issues**: [رابط المشاكل](https://github.com/your-username/suspects-data-management/issues)
- **البريد الإلكتروني**: <EMAIL>

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📈 خارطة الطريق

- [ ] دعم قواعد بيانات خارجية (MySQL, PostgreSQL)
- [ ] واجهة ويب للوصول عن بُعد
- [ ] تطبيق الهاتف المحمول
- [ ] تحليلات وتقارير متقدمة
- [ ] دعم اللغات المتعددة

---

**تم التطوير بـ ❤️ باستخدام أحدث التقنيات لضمان الأداء والأمان**

import fs from 'fs'
import path from 'path'
import { performance } from 'perf_hooks'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Performance testing utilities for Suspects Data Management
class PerformanceTest {
  constructor() {
    this.results = []
    this.testData = []
  }

  // Generate test data for performance testing
  generateTestData(count = 10000) {
    console.log(`🔄 Generating ${count} test records...`)
    const startTime = performance.now()
    
    const testData = []
    const firstNames = ['أحمد', 'محمد', 'علي', 'حسن', 'خالد', 'عبدالله', 'سعد', 'فهد', 'عبدالعزيز', 'سلطان']
    const lastNames = ['الأحمد', 'المحمد', 'العلي', 'الحسن', 'الخالد', 'العبدالله', 'السعد', 'الفهد', 'العبدالعزيز', 'السلطان']
    const cities = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'تبوك', 'أبها', 'حائل', 'القصيم']
    
    for (let i = 1; i <= count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
      const city = cities[Math.floor(Math.random() * cities.length)]
      
      testData.push({
        id: i,
        fullName: `${firstName} ${lastName}`,
        nationalId: `${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        phoneNumber: `05${Math.floor(Math.random() * 90000000) + 10000000}`,
        address: `${city} - حي ${Math.floor(Math.random() * 50) + 1}`,
        caseNumber: `CASE-${String(i).padStart(6, '0')}`,
        caseType: ['جنائية', 'مدنية', 'تجارية', 'أحوال شخصية'][Math.floor(Math.random() * 4)],
        status: ['قيد التحقيق', 'محال للمحكمة', 'مغلق', 'معلق'][Math.floor(Math.random() * 4)],
        createdDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        notes: `ملاحظات تجريبية للسجل رقم ${i} - ${firstName} ${lastName}`
      })
    }
    
    const endTime = performance.now()
    const generationTime = endTime - startTime
    
    console.log(`✅ Generated ${count} records in ${generationTime.toFixed(2)}ms`)
    this.testData = testData
    
    return {
      count,
      generationTime,
      data: testData
    }
  }

  // Test CSV export performance
  async testCSVExport(data) {
    console.log('🔄 Testing CSV export performance...')
    const startTime = performance.now()
    
    try {
      // Create CSV content
      const headers = Object.keys(data[0]).join(',')
      const rows = data.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' && value.includes(',') ? `"${value}"` : value
        ).join(',')
      )
      
      const csvContent = [headers, ...rows].join('\n')
      
      // Write to file
      const outputPath = path.join(__dirname, '../test-output.csv')
      fs.writeFileSync(outputPath, csvContent, 'utf8')
      
      const endTime = performance.now()
      const exportTime = endTime - startTime
      const fileSize = fs.statSync(outputPath).size
      
      console.log(`✅ CSV export completed in ${exportTime.toFixed(2)}ms`)
      console.log(`📁 File size: ${(fileSize / 1024 / 1024).toFixed(2)} MB`)
      
      // Clean up
      fs.unlinkSync(outputPath)
      
      return {
        recordCount: data.length,
        exportTime,
        fileSize,
        throughput: data.length / (exportTime / 1000) // records per second
      }
    } catch (error) {
      console.error('❌ CSV export failed:', error)
      throw error
    }
  }

  // Test search performance
  testSearchPerformance(data, searchTerms) {
    console.log('🔄 Testing search performance...')
    const results = []
    
    searchTerms.forEach(term => {
      const startTime = performance.now()
      
      // Simple text search simulation
      const matches = data.filter(record => {
        const searchText = Object.values(record).join(' ').toLowerCase()
        return searchText.includes(term.toLowerCase())
      })
      
      const endTime = performance.now()
      const searchTime = endTime - startTime
      
      results.push({
        term,
        matches: matches.length,
        searchTime,
        throughput: data.length / (searchTime / 1000)
      })
      
      console.log(`🔍 Search "${term}": ${matches.length} matches in ${searchTime.toFixed(2)}ms`)
    })
    
    return results
  }

  // Test memory usage
  getMemoryUsage() {
    const usage = process.memoryUsage()
    return {
      rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100, // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100, // MB
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100, // MB
      external: Math.round(usage.external / 1024 / 1024 * 100) / 100 // MB
    }
  }

  // Run comprehensive performance test
  async runFullTest() {
    console.log('🚀 Starting comprehensive performance test...')
    console.log('=' * 50)
    
    const testSizes = [1000, 5000, 10000, 50000, 100000]
    const searchTerms = ['أحمد', 'الرياض', 'جنائية', 'CASE-001', '0512345678']
    
    for (const size of testSizes) {
      console.log(`\n📊 Testing with ${size} records...`)
      
      // Memory before test
      const memoryBefore = this.getMemoryUsage()
      console.log(`💾 Memory before: ${memoryBefore.heapUsed} MB`)
      
      // Generate test data
      const generationResult = this.generateTestData(size)
      
      // Memory after generation
      const memoryAfterGeneration = this.getMemoryUsage()
      console.log(`💾 Memory after generation: ${memoryAfterGeneration.heapUsed} MB`)
      
      // Test search performance
      const searchResults = this.testSearchPerformance(this.testData, searchTerms)
      
      // Test CSV export
      const exportResult = await this.testCSVExport(this.testData)
      
      // Memory after tests
      const memoryAfter = this.getMemoryUsage()
      console.log(`💾 Memory after tests: ${memoryAfter.heapUsed} MB`)
      
      // Store results
      this.results.push({
        recordCount: size,
        generation: generationResult,
        search: searchResults,
        export: exportResult,
        memory: {
          before: memoryBefore,
          afterGeneration: memoryAfterGeneration,
          after: memoryAfter
        }
      })
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      console.log(`✅ Test completed for ${size} records`)
    }
    
    // Generate report
    this.generateReport()
  }

  // Generate performance report
  generateReport() {
    console.log('\n📋 PERFORMANCE TEST REPORT')
    console.log('=' * 50)
    
    const report = {
      timestamp: new Date().toISOString(),
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        memory: process.memoryUsage()
      },
      results: this.results
    }
    
    // Console summary
    console.log('\n📊 SUMMARY:')
    this.results.forEach(result => {
      console.log(`\n${result.recordCount} Records:`)
      console.log(`  Generation: ${result.generation.generationTime.toFixed(2)}ms`)
      console.log(`  Export: ${result.export.exportTime.toFixed(2)}ms (${result.export.throughput.toFixed(0)} records/sec)`)
      console.log(`  Memory Peak: ${result.memory.afterGeneration.heapUsed} MB`)
      
      const avgSearchTime = result.search.reduce((sum, s) => sum + s.searchTime, 0) / result.search.length
      console.log(`  Avg Search: ${avgSearchTime.toFixed(2)}ms`)
    })
    
    // Save detailed report
    const reportPath = path.join(__dirname, '../performance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📁 Detailed report saved to: ${reportPath}`)
    
    // Performance recommendations
    this.generateRecommendations()
  }

  // Generate performance recommendations
  generateRecommendations() {
    console.log('\n💡 PERFORMANCE RECOMMENDATIONS:')
    
    const largestTest = this.results[this.results.length - 1]
    const avgSearchTime = largestTest.search.reduce((sum, s) => sum + s.searchTime, 0) / largestTest.search.length
    
    if (avgSearchTime > 100) {
      console.log('⚠️  Search performance may be slow with large datasets')
      console.log('   Recommendation: Implement database indexing and full-text search')
    }
    
    if (largestTest.memory.afterGeneration.heapUsed > 500) {
      console.log('⚠️  High memory usage detected')
      console.log('   Recommendation: Implement data streaming and pagination')
    }
    
    if (largestTest.export.throughput < 1000) {
      console.log('⚠️  Export performance may be slow')
      console.log('   Recommendation: Implement streaming export for large datasets')
    }
    
    console.log('\n✅ For optimal performance in production:')
    console.log('   - Use SQLite with proper indexing')
    console.log('   - Implement full-text search (FTS5)')
    console.log('   - Use streaming for large file operations')
    console.log('   - Implement pagination for UI')
  }
}

// Run tests if called directly
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                     import.meta.url.endsWith(process.argv[1])

if (isMainModule) {
  console.log('🚀 Starting Performance Test Suite...')
  const tester = new PerformanceTest()

  // Run with garbage collection enabled
  if (process.argv.includes('--expose-gc')) {
    console.log('🗑️  Garbage collection enabled')
  }

  tester.runFullTest().catch(console.error)
}

export default PerformanceTest

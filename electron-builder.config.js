const { name, version, description, author } = require('./package.json')

module.exports = {
  appId: 'com.suspects.data.management',
  productName: 'برنامج بيانات المتهمين',
  copyright: `Copyright © ${new Date().getFullYear()} ${author}`,
  
  // Directories
  directories: {
    output: 'dist-electron',
    buildResources: 'build'
  },
  
  // Files to include
  files: [
    'dist/**/*',
    'electron/**/*',
    'node_modules/**/*',
    '!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}',
    '!node_modules/*/{test,__tests__,tests,powered-test,example,examples}',
    '!node_modules/*.d.ts',
    '!node_modules/.bin',
    '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}',
    '!.editorconfig',
    '!**/._*',
    '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}',
    '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}',
    '!**/{appveyor.yml,.travis.yml,circle.yml}',
    '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
  ],
  
  // Extra resources
  extraResources: [
    {
      from: 'public/icons',
      to: 'icons',
      filter: ['**/*']
    }
  ],
  
  // Security settings
  electronVersion: '28.0.0',
  nodeGypRebuild: false,
  buildDependenciesFromSource: false,
  
  // Windows configuration
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64', 'ia32']
      },
      {
        target: 'portable',
        arch: ['x64']
      }
    ],
    icon: 'public/icons/icon.ico',
    requestedExecutionLevel: 'asInvoker',
    
    // Digital signing (configure with your certificate)
    certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
    certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
    
    // Publisher information
    publisherName: 'Suspects Data Management',
    verifyUpdateCodeSignature: true,
    
    // File version info
    fileVersion: version,
    productVersion: version,
    
    // Additional properties
    legalTrademarks: 'Suspects Data Management',
    companyName: 'Suspects Data Management',
    fileDescription: description
  },
  
  // NSIS installer configuration
  nsis: {
    oneClick: false,
    perMachine: false,
    allowElevation: true,
    allowToChangeInstallationDirectory: true,
    installerIcon: 'public/icons/icon.ico',
    uninstallerIcon: 'public/icons/icon.ico',
    installerHeaderIcon: 'public/icons/icon.ico',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'برنامج بيانات المتهمين',
    
    // Multi-language support
    language: '1025', // Arabic
    
    // Custom installer script
    include: 'build/installer.nsh',
    
    // License
    license: 'LICENSE.txt',
    
    // Installer/Uninstaller messages in Arabic
    installerLanguages: ['ar', 'en'],
    
    // Run after install
    runAfterFinish: true,
    
    // Delete app data on uninstall
    deleteAppDataOnUninstall: false
  },
  
  // Portable configuration
  portable: {
    artifactName: '${productName}-${version}-portable.${ext}'
  },
  
  // Auto-updater configuration
  publish: [
    {
      provider: 'github',
      owner: 'your-github-username',
      repo: 'suspects-data-management',
      private: true
    }
  ],
  
  // macOS configuration (for future cross-platform support)
  mac: {
    category: 'public.app-category.productivity',
    icon: 'public/icons/icon.icns',
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: 'build/entitlements.mac.plist',
    entitlementsInherit: 'build/entitlements.mac.plist'
  },
  
  // Linux configuration (for future cross-platform support)
  linux: {
    target: [
      {
        target: 'AppImage',
        arch: ['x64']
      },
      {
        target: 'deb',
        arch: ['x64']
      }
    ],
    icon: 'public/icons',
    category: 'Office',
    desktop: {
      Name: 'برنامج بيانات المتهمين',
      Comment: description,
      Keywords: 'suspects;data;management;database'
    }
  },
  
  // Compression
  compression: 'maximum',
  
  // Build configuration
  beforeBuild: async (context) => {
    console.log('🔨 Starting build process...')
    
    // Verify required files exist
    const fs = require('fs')
    const path = require('path')
    
    const requiredFiles = [
      'dist/index.html',
      'electron/main.js',
      'electron/preload.js'
    ]
    
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`)
      }
    }
    
    console.log('✅ All required files verified')
  },
  
  afterSign: async (context) => {
    console.log('✅ Code signing completed')
    
    // Additional post-signing tasks
    if (process.platform === 'win32') {
      console.log('📝 Windows executable signed successfully')
    }
  },
  
  // Metadata
  metadata: {
    name,
    version,
    description,
    author,
    homepage: 'https://github.com/your-username/suspects-data-management',
    repository: {
      type: 'git',
      url: 'https://github.com/your-username/suspects-data-management.git'
    },
    bugs: {
      url: 'https://github.com/your-username/suspects-data-management/issues'
    },
    keywords: [
      'suspects',
      'data',
      'management',
      'database',
      'electron',
      'vue'
    ]
  }
}

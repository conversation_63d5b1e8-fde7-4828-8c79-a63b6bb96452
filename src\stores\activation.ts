import { defineStore } from 'pinia'
import { ref } from 'vue'

// Valid activation codes (stored securely in code)
const VALID_ACTIVATION_CODES = [
  '773$729#886',
  '777$3236#888', 
  '777$167#794'
]

// Storage key for activation status
const ACTIVATION_STORAGE_KEY = 'app_activation_status'
const ACTIVATION_DATE_KEY = 'app_activation_date'

export const useActivationStore = defineStore('activation', () => {
  // State
  const isActivated = ref(false)
  const activationDate = ref<Date | null>(null)
  const isLoading = ref(false)

  // Actions
  const validateActivationCode = async (code: string): Promise<boolean> => {
    isLoading.value = true
    
    try {
      // Simulate network delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Check if code matches any valid codes
      const trimmedCode = code.trim()
      const isValid = VALID_ACTIVATION_CODES.includes(trimmedCode)
      
      if (isValid) {
        console.log('✅ Activation code validated successfully')
        return true
      } else {
        console.log('❌ Invalid activation code provided')
        return false
      }
    } catch (error) {
      console.error('Error validating activation code:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }

  const saveActivationStatus = async (): Promise<void> => {
    try {
      const currentDate = new Date()
      
      // Save to localStorage
      localStorage.setItem(ACTIVATION_STORAGE_KEY, 'true')
      localStorage.setItem(ACTIVATION_DATE_KEY, currentDate.toISOString())
      
      // Save to IndexedDB for additional security
      if (window.electronAPI?.saveActivationStatus) {
        await window.electronAPI.saveActivationStatus({
          activated: true,
          date: currentDate.toISOString(),
          timestamp: Date.now()
        })
      }
      
      // Update store state
      isActivated.value = true
      activationDate.value = currentDate
      
      console.log('✅ Activation status saved successfully')
    } catch (error) {
      console.error('Error saving activation status:', error)
      throw error
    }
  }

  const checkActivationStatus = async (): Promise<boolean> => {
    try {
      // Check localStorage first
      const localStorageStatus = localStorage.getItem(ACTIVATION_STORAGE_KEY)
      const localStorageDate = localStorage.getItem(ACTIVATION_DATE_KEY)
      
      if (localStorageStatus === 'true' && localStorageDate) {
        isActivated.value = true
        activationDate.value = new Date(localStorageDate)
        
        // Verify with Electron main process if available
        if (window.electronAPI?.checkActivationStatus) {
          const electronStatus = await window.electronAPI.checkActivationStatus()
          if (electronStatus?.activated) {
            console.log('✅ Activation status verified from both sources')
            return true
          }
        } else {
          // Web version - rely on localStorage
          console.log('✅ Activation status verified from localStorage')
          return true
        }
      }
      
      // Check Electron storage if localStorage is empty
      if (window.electronAPI?.checkActivationStatus) {
        const electronStatus = await window.electronAPI.checkActivationStatus()
        if (electronStatus?.activated) {
          // Sync back to localStorage
          localStorage.setItem(ACTIVATION_STORAGE_KEY, 'true')
          localStorage.setItem(ACTIVATION_DATE_KEY, electronStatus.date)
          
          isActivated.value = true
          activationDate.value = new Date(electronStatus.date)
          
          console.log('✅ Activation status restored from Electron storage')
          return true
        }
      }
      
      console.log('❌ No valid activation status found')
      return false
    } catch (error) {
      console.error('Error checking activation status:', error)
      return false
    }
  }

  const resetActivation = async (): Promise<void> => {
    try {
      // Clear localStorage
      localStorage.removeItem(ACTIVATION_STORAGE_KEY)
      localStorage.removeItem(ACTIVATION_DATE_KEY)
      
      // Clear Electron storage
      if (window.electronAPI?.clearActivationStatus) {
        await window.electronAPI.clearActivationStatus()
      }
      
      // Reset store state
      isActivated.value = false
      activationDate.value = null
      
      console.log('✅ Activation status reset successfully')
    } catch (error) {
      console.error('Error resetting activation status:', error)
      throw error
    }
  }

  const getActivationInfo = () => {
    return {
      isActivated: isActivated.value,
      activationDate: activationDate.value,
      daysActivated: activationDate.value 
        ? Math.floor((Date.now() - activationDate.value.getTime()) / (1000 * 60 * 60 * 24))
        : 0
    }
  }

  // Initialize activation status on store creation
  const initializeActivation = async () => {
    await checkActivationStatus()
  }

  return {
    // State
    isActivated,
    activationDate,
    isLoading,
    
    // Actions
    validateActivationCode,
    saveActivationStatus,
    checkActivationStatus,
    resetActivation,
    getActivationInfo,
    initializeActivation
  }
})

// Type definitions for Electron API
declare global {
  interface Window {
    electronAPI?: {
      saveActivationStatus: (data: any) => Promise<void>
      checkActivationStatus: () => Promise<any>
      clearActivationStatus: () => Promise<void>
    }
  }
}
